package department

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/requests"
	"gitlab.finema.co/finema/finework/finework-api/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type DepartmentController struct {
}

func (m DepartmentController) Pagination(c core.IHTTPContext) error {
	input := &requests.DepartmentPaginationRequest{}
	if err := c.Bind(input); err != nil {
		ierr := core.Error{
			Status:  http.StatusBadRequest,
			Code:    "INVALID_PARAMS",
			Message: "Invalid params request",
		}
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	departmentSvc := services.NewDepartmentService(c)
	res, ierr := departmentSvc.Pagination(c.GetPageOptions(), &services.DepartmentPaginationOptions{
		MinistryID: input.MinistryID,
	})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m DepartmentController) Find(c core.IHTTPContext) error {
	departmentSvc := services.NewDepartmentService(c)
	department, err := departmentSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, department)
}

func (m DepartmentController) Create(c core.IHTTPContext) error {
	input := &requests.DepartmentCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	departmentSvc := services.NewDepartmentService(c)
	payload := &services.DepartmentCreatePayload{}
	_ = utils.Copy(payload, input)
	department, err := departmentSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, department)
}

func (m DepartmentController) Update(c core.IHTTPContext) error {
	input := &requests.DepartmentUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	departmentSvc := services.NewDepartmentService(c)
	payload := &services.DepartmentUpdatePayload{}
	_ = utils.Copy(payload, input)
	department, err := departmentSvc.Update(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, department)
}

func (m DepartmentController) Delete(c core.IHTTPContext) error {
	departmentSvc := services.NewDepartmentService(c)
	err := departmentSvc.Delete(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
