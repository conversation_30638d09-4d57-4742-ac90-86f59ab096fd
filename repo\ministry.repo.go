package repo

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type MinistryOption func(repository.IRepository[models.Ministry])

var Ministry = func(c core.IContext, options ...MinistryOption) repository.IRepository[models.Ministry] {
	r := repository.New[models.Ministry](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func MinistryOrderBy(pageOptions *core.PageOptions) MinistryOption {
	return func(c repository.IRepository[models.Ministry]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("name_th ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func MinistryWithDepartments() MinistryOption {
	return func(c repository.IRepository[models.Ministry]) {
		c.Preload("Departments")
	}
}

func MinistryWithSearch(q string) MinistryOption {
	return func(c repository.IRepository[models.Ministry]) {
		if q == "" {
			return
		}
		searchTerm := "%" + q + "%"
		c.Where("name_th ILIKE ? OR name_en ILIKE ?", searchTerm, searchTerm)
	}
}
