package repo

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type SgaOption func(repository.IRepository[models.Sga])

var Sga = func(c core.IContext, options ...SgaOption) repository.IRepository[models.Sga] {
	r := repository.New[models.Sga](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func SgaOrderBy(pageOptions *core.PageOptions) SgaOption {
	return func(c repository.IRepository[models.Sga]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("name ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func SgaWithSearch(q string) SgaOption {
	return func(c repository.IRepository[models.Sga]) {
		if q == "" {
			return
		}
		searchTerm := "%" + q + "%"
		c.Where("name ILIKE ?", searchTerm)
	}
}
