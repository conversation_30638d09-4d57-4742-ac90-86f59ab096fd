package requests

import (
	"fmt"
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type CheckinOnThisDayRequest struct {
	core.BaseValidator
	Date     *string   `json:"date" query:"date"`
	TeamCode []string  `json:"team_code" query:"team_code"`
	UserID   *string   `json:"user_id" query:"user_id"`
	Type     *string   `json:"type" query:"type"`
}

func (r *CheckinOnThisDayRequest) Validate(ctx core.IContext) core.IError {
	// Date is required for this endpoint
	r.Must(r.IsStrRequired(r.Date, "date"))
	r.Must(r.IsDate(r.Date, "date"))

	// Validate team_code exists if provided
	if len(r.TeamCode) > 0 {
		for i, teamCode := range r.TeamCode {
			fieldName := fmt.Sprintf("team_code[%d]", i)
			teamCodeNoSpace := strings.TrimSpace(teamCode)
			r.Must(r.IsExists(ctx, utils.ToPointer(teamCodeNoSpace), models.Team{}.TableName(), "code", fieldName))
		}
	}

	// Validate user_id exists if provided
	if r.UserID != nil {
		r.Must(r.IsExists(ctx, r.UserID, models.User{}.TableName(), "id", "user_id"))
	}

	// Validate type if provided
	if r.Type != nil {
		validTypes := []string{
			string(models.CheckinTypeOfficeHQ), string(models.CheckinTypeWfh),
			string(models.CheckinTypeOnsite), string(models.CheckinTypeOfficeAKV),
			string(models.CheckinTypeBusinessTrip), string(models.CheckinTypeAnnual),
			string(models.CheckinTypeSick), string(models.CheckinTypeMenstrual),
			string(models.CheckinTypeBirthday), string(models.CheckinTypeOrdination),
			string(models.CheckinTypeBusiness),
		}
		r.Must(r.IsStrIn(r.Type, strings.Join(validTypes, "|"), "type"))
	}

	return r.Error()
}
