package requests

import (
	"fmt"
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type TimesheetCreateItem struct {
	ProjectCode *string  `json:"project_code"`
	SgaID       *string  `json:"sga_id"`
	Timing      *float64 `json:"timing"`
	Type        *string  `json:"type"`
	LeaveType   *string  `json:"leave_type"`
	Description *string  `json:"description"`
	Date        *string  `json:"date"`
}

type TimesheetCreate struct {
	core.BaseValidator
	Items []TimesheetCreateItem `json:"items"`
}

func (r *TimesheetCreate) Valid(ctx core.IContext) core.IError {
	// Validate that items array is not empty
	if r.Must(r.IsRequired(r.Items, "items")) && r.Must(r.<PERSON>y<PERSON>in(r.Items, 1, "items")) {
		// Validate each item in the array
		for i, item := range r.Items {
			fieldPrefix := fmt.Sprintf("items[%d]", i)

			// Validate type
			r.Must(r.IsStrIn(item.Type, strings.Join([]string{string(models.TimesheetTypeProject), string(models.TimesheetTypeSga),
				string(models.TimesheetTypeLeave), string(models.TimesheetTypeInternal),
				string(models.TimesheetTypeExternal), string(models.TimesheetTypeOt)}, "|"), fieldPrefix+".type"))

			if r.Must(r.IsStrRequired(item.Type, fieldPrefix+".type")) {
				switch utils.ToNonPointer(item.Type) {
				case string(models.TimesheetTypeLeave):
					r.Items[i].ProjectCode = nil
					r.Items[i].SgaID = nil
					r.Must(r.IsStrRequired(item.LeaveType, fieldPrefix+".leave_type"))
					r.Must(r.IsStrIn(item.LeaveType, strings.Join([]string{string(models.LeaveTypeAnnual), string(models.LeaveTypeSick),
						string(models.LeaveTypeBusiness), string(models.LeaveTypeMenstrual),
						string(models.LeaveTypeBirthday), string(models.LeaveTypeOrdination)}, "|"), fieldPrefix+".leave_type"))
				case string(models.TimesheetTypeProject):
					r.Items[i].SgaID = nil
					r.Items[i].LeaveType = nil
					r.Must(r.IsStrRequired(item.ProjectCode, fieldPrefix+".project_code"))
				case string(models.TimesheetTypeSga):
					r.Items[i].ProjectCode = nil
					r.Items[i].LeaveType = nil
					r.Must(r.IsStrRequired(item.SgaID, fieldPrefix+".sga_id"))
				case  string(models.TimesheetTypeInternal), string(models.TimesheetTypeExternal): 
					r.Items[i].ProjectCode = nil
					r.Items[i].SgaID = nil
					r.Items[i].LeaveType = nil
				}

			}

			// Validate date
			if r.Must(r.IsRequired(item.Date, fieldPrefix+".date")) {
				r.Must(r.IsDate(item.Date, fieldPrefix+".date"))
			}

			// Validate timing
			r.Must(r.IsRequired(item.Timing, fieldPrefix+".timing"))
		}
	}

	return r.Error()
}
