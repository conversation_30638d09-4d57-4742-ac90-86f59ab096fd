package requests

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type TimesheetPaginationRequest struct {
	core.BaseValidator
	StartDate   *string `json:"start_date" query:"start_date"`
	EndDate     *string `json:"end_date" query:"end_date"`
	UserID      *string `json:"user_id" query:"user_id"`
	ProjectCode *string `json:"project_code" query:"project_code"`
	TeamCode    *string `json:"team_code" query:"team_code"`
	Search      *string `json:"search" query:"search"`
}

func (r *TimesheetPaginationRequest) Validate(ctx core.IContext) core.IError {
	r.Must(r.IsDate(r.StartDate, "start_date"))
	r.Must(r.IsDate(r.EndDate, "end_date"))

	// Validate user_id exists if provided
	if r.UserID != nil {
		r.Must(r.IsExists(ctx, r.User<PERSON>, models.User{}.TableName(), "id", "user_id"))
	}

	return r.Error()
}
