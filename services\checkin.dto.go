package services

import "time"

type CheckinCreateItem struct {
	Type     string
	Period   string
	Location *string
	Remarks  *string
	IsUnused *bool
	Date     *time.Time `copier:"must"`
}

type CheckinCreatePayload struct {
	UserId string
	Items  []CheckinCreateItem
}

type CheckinUpdatePayload struct {
	UserId   string
	Type     string
	Period   string
	Location *string
	Remarks  *string
	IsUnused *bool
	Date     *time.Time
}

type CheckinPaginationOptions struct {
	UserID    *string  `json:"user_id"`
	StartDate *string  `json:"start_date"`
	EndDate   *string  `json:"end_date"`
	TeamCode  []string `json:"team_code"`
	Type      *string  `json:"type"`
}
