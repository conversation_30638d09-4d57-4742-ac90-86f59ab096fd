package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type ISgaService interface {
	Create(input *SgaCreatePayload) (*models.Sga, core.IError)
	Update(id string, input *SgaUpdatePayload) (*models.Sga, core.IError)
	Find(id string) (*models.Sga, core.IError)
	Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Sga], core.IError)
	Delete(id string) core.IError
}

type SgaService struct {
	ctx core.IContext
}

func (s SgaService) Create(input *SgaCreatePayload) (*models.Sga, core.IError) {
	sga := &models.Sga{
		BaseModelHardDelete: models.NewBaseModelHardDelete(),
		Name:                input.Name,
		Description:         utils.ToPointer(input.Description),
	}

	ierr := repo.Sga(s.ctx).Create(sga)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(sga.ID)
}

func (s SgaService) Update(id string, input *SgaUpdatePayload) (*models.Sga, core.IError) {
	sga, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Update fields if provided
	if input.Name != "" {
		sga.Name = input.Name
	}

	if input.Description != "" {
		sga.Description = utils.ToPointer(input.Description)
	}

	ierr = repo.Sga(s.ctx).Where("id = ?", id).Updates(sga)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(sga.ID)
}

func (s SgaService) Find(id string) (*models.Sga, core.IError) {
	return repo.Sga(s.ctx).FindOne("id = ?", id)
}

func (s SgaService) Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Sga], core.IError) {
	return repo.Sga(s.ctx, repo.SgaOrderBy(pageOptions), repo.SgaWithSearch(pageOptions.Q)).Pagination(pageOptions)
}

func (s SgaService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.Sga(s.ctx).Delete("id = ?", id)
}

func NewSgaService(ctx core.IContext) ISgaService {
	return &SgaService{ctx: ctx}
}
