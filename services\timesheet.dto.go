package services

type TimesheetCreateItem struct {
	SgaID       *string
	ProjectCode *string
	Timing      float64
	Type        string
	LeaveType   *string
	Description *string
	Date        string
}

type TimesheetCreatePayload struct {
	UserID string
	Items  []TimesheetCreateItem
}

type TimesheetUpdatePayload struct {
	UserID      string
	SgaID       *string
	ProjectCode *string
	Timing      float64
	Type        string
	LeaveType   *string
	Description *string
	Date        string
}

type TimesheetPaginationOptions struct {
	UserID      *string `json:"user_id"`
	StartDate   *string `json:"start_date"`
	EndDate     *string `json:"end_date"`
	ProjectCode *string `json:"project_code"`
	TeamCode    *string `json:"team_code"`
	Search      *string `json:"search"`
}

type TimesheetSummaryReportOptions struct {
	StartDate *string `json:"start_date"`
	EndDate   *string `json:"end_date"`
	TeamCode  *string `json:"team_code"`
}
