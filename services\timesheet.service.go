package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	"gitlab.finema.co/finema/finework/finework-api/views"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type ITimesheetService interface {
	Create(input *TimesheetCreatePayload) ([]models.Timesheet, core.IError)
	Update(id string, input *TimesheetUpdatePayload) (*models.Timesheet, core.IError)
	Find(id string) (*models.Timesheet, core.IError)
	Pagination(pageOptions *core.PageOptions, options *TimesheetPaginationOptions) (*repository.Pagination[models.Timesheet], core.IError)
	SummaryReport(options *TimesheetSummaryReportOptions) ([]views.TimesheetSummaryReportView, core.IError)
	Delete(id string) core.IError
}

type timesheetService struct {
	ctx core.IContext
}

func (s timesheetService) Create(input *TimesheetCreatePayload) ([]models.Timesheet, core.IError) {
	timesheets := []models.Timesheet{}
	for _, item := range input.Items {
		var leaveType *models.LeaveType
		if item.LeaveType != nil {
			leaveTypeValue := models.LeaveType(utils.ToNonPointer(item.LeaveType))
			leaveType = &leaveTypeValue
		}

		timesheet := models.Timesheet{
			BaseModelHardDelete: models.NewBaseModelHardDelete(),
			UserID:              input.UserID,
			SgaID:               item.SgaID,
			ProjectCode:         item.ProjectCode,
			Timing:              item.Timing,
			Type:                models.TimesheetType(item.Type),
			LeaveType:           leaveType,
			Description:         item.Description,
			Date:                item.Date,
		}
		timesheets = append(timesheets, timesheet)
	}

	ierr := repo.Timesheet(s.ctx).Create(timesheets)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return timesheets, nil
}

func (s timesheetService) Update(id string, input *TimesheetUpdatePayload) (*models.Timesheet, core.IError) {
	timesheet, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	if input.SgaID != nil {
		timesheet.SgaID = input.SgaID
	}
	if input.ProjectCode != nil {
		timesheet.ProjectCode = input.ProjectCode
	}
	if input.Timing != 0 {
		timesheet.Timing = input.Timing
	}
	if input.Type != "" {
		timesheet.Type = models.TimesheetType(input.Type)
	}
	if input.LeaveType != nil {
		timesheet.LeaveType = utils.ToPointer(models.LeaveType(utils.ToNonPointer(input.LeaveType)))
	}
	if input.Description != nil {
		timesheet.Description = utils.ToPointer(*input.Description)
	}
	if input.Date != "" {
		timesheet.Date = input.Date
	}

	ierr = repo.Timesheet(s.ctx).Where("id = ?", id).Updates(timesheet)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(timesheet.ID)
}

func (s timesheetService) Find(id string) (*models.Timesheet, core.IError) {
	return repo.Timesheet(s.ctx, repo.TimesheetWithAllRelation()).FindOne("id = ?", id)
}

func (s timesheetService) Pagination(pageOptions *core.PageOptions, options *TimesheetPaginationOptions) (*repository.Pagination[models.Timesheet], core.IError) {
	return repo.Timesheet(
		s.ctx,
		repo.TimesheetWithAllRelation(),
		repo.TimesheetWithUser(options.UserID),
		repo.TimesheetWithDateRange(options.StartDate, options.EndDate),
		// repo.TimesheetWithKeywordType(options.Search),
		repo.TimesheetWithProjectCode(options.ProjectCode),
		repo.TimesheetWithTeamCode(options.TeamCode),
		repo.TimesheetOrderBy(pageOptions)).
		Pagination(pageOptions)
}
func (s timesheetService) SummaryReport(options *TimesheetSummaryReportOptions) ([]views.TimesheetSummaryReportView, core.IError) {
	users, ierr := repo.User(s.ctx,
		repo.UserWithTeam(options.TeamCode),
		repo.UserWithTimeSheetDateRange(options.StartDate, options.EndDate),
	).
		Preload("Timesheets.Project").
		Preload("Timesheets.Sga").
		Preload("Team").
		FindAll()
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	res := make([]views.TimesheetSummaryReportView, len(users))

	for i, user := range users {
		// Calculate total timing for the user
		var totalTiming float64
		var totalProjectTiming float64
		var totalLeaveTiming float64
		var totalSgaTiming float64
		var totalInternalTiming float64
		var totalExternalTiming float64
		var totalOtTiming float64

		// Use map to aggregate project timings by unique key (ProjectCode + Type)
		projectTimingMap := make(map[string]*views.TimesheetSummaryReportProjectTiming)

		for _, timesheet := range user.Timesheets {
			totalTiming += timesheet.Timing

			// Calculate timing by type
			switch timesheet.Type {
			case models.TimesheetTypeLeave:
				totalLeaveTiming += timesheet.Timing
			case models.TimesheetTypeSga:
				totalSgaTiming += timesheet.Timing
			case models.TimesheetTypeInternal:
				totalInternalTiming += timesheet.Timing
			case models.TimesheetTypeExternal:
				totalExternalTiming += timesheet.Timing
			case models.TimesheetTypeOt:
				totalOtTiming += timesheet.Timing
			}

			// Create unique key for grouping (ProjectCode + Type + SgaID)
			var key string
			var projectCode *string
			var projectName *string
			var sgaName *string

			if timesheet.Project != nil {
				totalProjectTiming += timesheet.Timing
				projectCode = timesheet.ProjectCode
				projectName = &timesheet.Project.Name
				if projectCode != nil {
					key = *projectCode + "|" + string(timesheet.Type)
				} else {
					key = "nil|" + string(timesheet.Type)
				}
			} else if timesheet.Sga != nil {
				sgaName = &timesheet.Sga.Name
				if timesheet.SgaID != nil {
					key = *timesheet.SgaID + "|" + string(timesheet.Type)
				} else {
					key = "nil|" + string(timesheet.Type)
				}
			} else {
				projectCode = nil
				projectName = nil
				key = "nil|" + string(timesheet.Type)
			}

			// Check if this combination already exists in map
			if existingTiming, exists := projectTimingMap[key]; exists {
				// Add timing to existing entry
				existingTiming.TotalTiming += timesheet.Timing
			} else {
				// Create new entry
				projectTimingMap[key] = &views.TimesheetSummaryReportProjectTiming{
					ProjectCode: projectCode,
					ProjectName: projectName,
					SgaName:     sgaName,
					Type:        string(timesheet.Type),
					TotalTiming: timesheet.Timing,
				}
			}
		}

		// Convert map to slice
		timings := make([]views.TimesheetSummaryReportProjectTiming, 0, len(projectTimingMap))
		for _, timing := range projectTimingMap {
			timings = append(timings, *timing)
		}

		res[i] = views.TimesheetSummaryReportView{
			User:                &user,
			TotalTiming:         totalTiming,
			TotalProjectTiming:  totalProjectTiming,
			TotalLeaveTiming:    totalLeaveTiming,
			TotalSgaTiming:      totalSgaTiming,
			TotalInternalTiming: totalInternalTiming,
			TotalExternalTiming: totalExternalTiming,
			TotalOtTiming:       totalOtTiming,
			Timings:             timings,
		}
	}

	return res, nil
}

func (s timesheetService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.Timesheet(s.ctx).Delete("id = ?", id)
}

func NewTimesheetService(ctx core.IContext) ITimesheetService {
	return &timesheetService{ctx: ctx}
}
